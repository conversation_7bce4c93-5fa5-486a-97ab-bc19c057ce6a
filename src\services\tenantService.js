// src/services/tenantService.js
import multiTenantApi from '../core/api/multiTenantApi';

/**
 * Tenant Service - High-level service for tenant operations
 * Uses the multiTenantApi internally but provides a cleaner interface
 */
class TenantService {
  constructor() {
    this.currentTenant = null;
    this.currentLanguage = 'en';
  }

  /**
   * Set current tenant context
   * @param {number} tenantId - Tenant ID
   * @param {string} language - Language code
   */
  setContext(tenantId, language = 'en') {
    this.currentTenant = tenantId;
    this.currentLanguage = language;
  }

  /**
   * Get tenant header information
   * @param {number} tenantId - Optional tenant ID (uses current if not provided)
   * @param {string} lang - Optional language (uses current if not provided)
   * @returns {Promise<Object>} Header information
   */
  async getHeaderInfo(tenantId = null, lang = null) {
    const id = tenantId || this.currentTenant;
    const language = lang || this.currentLanguage;
    
    try {
      const result = await multiTenantApi.getTenantHeaderInfo(id, language);
      
      if (result.success) {
        return {
          success: true,
          data: this.processHeaderData(result.data),
          raw: result.data
        };
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Failed to fetch header info'
      };
    }
  }

  /**
   * Get tenant footer information
   * @param {number} tenantId - Optional tenant ID
   * @param {string} lang - Optional language
   * @returns {Promise<Object>} Footer information
   */
  async getFooterInfo(tenantId = null, lang = null) {
    const id = tenantId || this.currentTenant;
    const language = lang || this.currentLanguage;
    
    try {
      const result = await multiTenantApi.getTenantFooterInfo(id, language);
      
      if (result.success) {
        return {
          success: true,
          data: this.processFooterData(result.data),
          raw: result.data
        };
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Failed to fetch footer info'
      };
    }
  }

  /**
   * Get tenant programs
   * @param {number} tenantId - Optional tenant ID
   * @param {string} lang - Optional language
   * @returns {Promise<Object>} Programs list
   */
  async getPrograms(tenantId = null, lang = null) {
    const id = tenantId || this.currentTenant;
    const language = lang || this.currentLanguage;
    
    try {
      const result = await multiTenantApi.getTenantPrograms(id, language);
      
      if (result.success) {
        return {
          success: true,
          data: this.processProgramsData(result.data),
          raw: result.data
        };
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Failed to fetch programs'
      };
    }
  }

  /**
   * Get all tenant data at once
   * @param {number} tenantId - Optional tenant ID
   * @param {string} lang - Optional language
   * @returns {Promise<Object>} All tenant data
   */
  async getAllData(tenantId = null, lang = null) {
    const id = tenantId || this.currentTenant;
    const language = lang || this.currentLanguage;
    
    try {
      const result = await multiTenantApi.getAllTenantData(id, language);
      
      if (result.success) {
        return {
          success: true,
          data: {
            header: result.data.header?.success ? this.processHeaderData(result.data.header.data) : null,
            footer: result.data.footer?.success ? this.processFooterData(result.data.footer.data) : null,
            programs: result.data.programs?.success ? this.processProgramsData(result.data.programs.data) : null
          },
          raw: result.data,
          errors: result.errors
        };
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Failed to fetch tenant data'
      };
    }
  }

  /**
   * Test connection to tenant API
   * @param {string} token - Optional token
   * @returns {Promise<Object>} Connection status
   */
  async testConnection(token = null) {
    return await multiTenantApi.testConnection(token);
  }

  /**
   * Clear cached data
   */
  clearCache() {
    multiTenantApi.clearCache();
  }

  /**
   * Process header data to extract useful information
   * @param {Object} rawData - Raw header data from API
   * @returns {Object} Processed header data
   */
  processHeaderData(rawData) {
    if (!rawData) return null;

    return {
      name: rawData.name || 'Default Tenant',
      logo: rawData.logo || null,
      colors: {
        primary: rawData.primarycolor || '#8c76dd',
        secondary: rawData.secondarycolor || '#FFE284',
        tertiary: rawData.teritorycolor || '#FF8A19'
      },
      services: rawData.services || [],
      features: {
        hasAboutUs: rawData.aboutus?.[0]?.status === '1',
        hasContactUs: !!rawData.contactus,
        hasFaq: rawData.faq?.[0]?.status === '1',
        hasServices: rawData.services?.length > 0
      },
      links: {
        aboutUs: rawData.aboutus?.[0]?.link || '/about-us',
        contactUs: rawData.contactus || '/contact-us',
        faq: rawData.faq?.[0]?.link || '/faq'
      },
      expirationDate: rawData.expirationdate ? new Date(rawData.expirationdate * 1000) : null
    };
  }

  /**
   * Process footer data
   * @param {Object} rawData - Raw footer data from API
   * @returns {Object} Processed footer data
   */
  processFooterData(rawData) {
    if (!rawData) return null;

    return {
      // Add footer processing logic here
      ...rawData
    };
  }

  /**
   * Process programs data
   * @param {Object} rawData - Raw programs data from API
   * @returns {Array} Processed programs array
   */
  processProgramsData(rawData) {
    if (!rawData || !Array.isArray(rawData)) return [];

    return rawData.map(program => ({
      id: program.id,
      name: program.name,
      description: program.description,
      image: program.image,
      // Add more program processing logic here
      ...program
    }));
  }
}

// Create singleton instance
const tenantService = new TenantService();

export default tenantService;
