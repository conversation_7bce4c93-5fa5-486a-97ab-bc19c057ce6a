import { MOODLE_CONFIG, API_BASE_URL } from '../../config/config';

/**
 * Multi-Tenant Moodle API Service
 * Handles all Moodle Web Service calls for multi-tenant functionality
 */
class MultiTenantApi {
  constructor() {
    this.baseUrl = `${API_BASE_URL}${MOODLE_CONFIG.BASE_URL}`;
    this.defaultToken = MOODLE_CONFIG.DEFAULT_WS_TOKEN;
    this.defaultTenant = MOODLE_CONFIG.DEFAULT_TENANT;

    // Cache للبيانات المتكررة
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cached data
   * @param {string} key - Cache key
   * @returns {Object|null} Cached data or null
   */
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Set cached data
   * @param {string} key - Cache key
   * @param {Object} data - Data to cache
   */
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Build Moodle API request URL
   * @param {string} wsfunction - Web service function name
   * @param {Object} params - Additional parameters
   * @param {string} token - Web service token (optional)
   * @returns {string} Complete API URL
   */
  buildApiUrl(wsfunction, params = {}, token = null) {
    const wstoken = token || this.defaultToken;
    const baseParams = {
      wstoken,
      wsfunction,
      moodlewsrestformat: 'json'
    };

    const allParams = { ...baseParams, ...params };
    const queryString = new URLSearchParams(allParams).toString();

    return `${this.baseUrl}?${queryString}`;
  }

  /**
   * Handle API response with consistent error format
   * @param {Response} response - Fetch response
   * @returns {Promise<Object>} Processed response
   */
  async handleResponse(response) {
    try {
      if (!response.ok) {
        return {
          success: false,
          data: null,
          message: `HTTP Error: ${response.status} ${response.statusText}`,
          status: response.status
        };
      }

      const data = await response.json();

      if (data.exception) {
        return {
          success: false,
          data: null,
          message: `Moodle API Error: ${data.message || data.exception}`,
          status: 400
        };
      }

      return {
        success: true,
        data: data,
        message: 'Success',
        status: 200
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Unknown error occurred',
        status: 500
      };
    }
  }

  /**
   * Make API call to Moodle Web Service with retry mechanism
   * @param {string} wsfunction - Web service function name
   * @param {Object} params - API parameters
   * @param {string} token - Web service token (optional)
   * @param {number} retries - Number of retry attempts
   * @returns {Promise<Object>} API response
   */
  async callApi(wsfunction, params = {}, token = null, retries = 2) {
    const cacheKey = `${wsfunction}_${JSON.stringify(params)}_${token || 'default'}`;

    // Check cache first for GET-like operations
    if (['GET_TENANT_HEADER_INFO', 'GET_FOOTER_INFO', 'GET_PROGRAMS'].includes(wsfunction)) {
      const cachedData = this.getCachedData(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const url = this.buildApiUrl(wsfunction, params, token);

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'ngrok-skip-browser-warning': 'true'
          }
        });

        const result = await this.handleResponse(response);

        // Cache successful responses
        if (result.success && ['GET_TENANT_HEADER_INFO', 'GET_FOOTER_INFO', 'GET_PROGRAMS'].includes(wsfunction)) {
          this.setCachedData(cacheKey, result);
        }

        return result;
      } catch (error) {
        if (attempt === retries) {
          return {
            success: false,
            data: null,
            message: error.message || 'Network error occurred',
            status: 500
          };
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  /**
   * Get Tenant Header Information
   * @param {number} tenantId - Tenant ID
   * @param {string} lang - Language code (en, ar, etc.)
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} Header information with consistent format
   */
  async getTenantHeaderInfo(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_TENANT_HEADER_INFO, params, token);
  }

  /**
   * Get Tenant Footer Information
   * @param {number} tenantId - Tenant ID
   * @param {string} lang - Language code
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} Footer information with consistent format
   */
  async getTenantFooterInfo(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_FOOTER_INFO, params, token);
  }

  /**
   * Get Tenant Programs
   * @param {number} tenantId - Tenant ID
   * @param {string} lang - Language code
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} Programs list with consistent format
   */
  async getTenantPrograms(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_PROGRAMS, params, token);
  }

  /**
   * Test API Connection
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} Connection status with details
   */
  async testConnection(token = null) {
    try {
      const result = await this.getTenantHeaderInfo(null, 'en', token);
      return {
        success: result.success,
        connected: result.success,
        message: result.success ? 'Connection successful' : result.message,
        data: result.success ? result.data : null
      };
    } catch (error) {
      return {
        success: false,
        connected: false,
        message: error.message || 'Connection failed',
        data: null
      };
    }
  }

  /**
   * Get all tenant data at once
   * @param {number} tenantId - Tenant ID
   * @param {string} lang - Language code
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} All tenant data
   */
  async getAllTenantData(tenantId = null, lang = 'en', token = null) {
    try {
      const [headerResult, footerResult, programsResult] = await Promise.allSettled([
        this.getTenantHeaderInfo(tenantId, lang, token),
        this.getTenantFooterInfo(tenantId, lang, token),
        this.getTenantPrograms(tenantId, lang, token)
      ]);

      return {
        success: true,
        data: {
          header: headerResult.status === 'fulfilled' ? headerResult.value : null,
          footer: footerResult.status === 'fulfilled' ? footerResult.value : null,
          programs: programsResult.status === 'fulfilled' ? programsResult.value : null
        },
        errors: {
          header: headerResult.status === 'rejected' ? headerResult.reason : null,
          footer: footerResult.status === 'rejected' ? footerResult.reason : null,
          programs: programsResult.status === 'rejected' ? programsResult.reason : null
        }
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Failed to fetch tenant data'
      };
    }
  }
}

// Create singleton instance
const multiTenantApi = new MultiTenantApi();

// Add global debug helper in development
if (process.env.NODE_ENV === 'development') {
  window.multiTenantApi = multiTenantApi;
}

export default multiTenantApi;

// Named exports for specific functions
export const {
  getTenantHeaderInfo,
  getTenantFooterInfo,
  getTenantPrograms,
  getAllTenantData,
  testConnection,
  callApi,
  buildApiUrl,
  clearCache
} = multiTenantApi;
