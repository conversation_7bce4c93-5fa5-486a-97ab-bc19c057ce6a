import { MOODLE_CONFIG, API_BASE_URL } from '../../config/config';
import { postRequest } from './fetcher';

// Configuration
const config = {
  baseUrl: `${API_BASE_URL}${MOODLE_CONFIG.BASE_URL}`,
  defaultToken: MOODLE_CONFIG.DEFAULT_WS_TOKEN,
  defaultTenant: MOODLE_CONFIG.DEFAULT_TENANT
};

// Cache for performance
let cache = new Map();
const cacheTimeout = 5 * 60 * 1000; // 5 minutes

/**
 * Clear cache
 */
const clearCache = () => {
  cache.clear();
};

/**
 * Get cached data
 */
const getCachedData = (key) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < cacheTimeout) {
    return cached.data;
  }
  cache.delete(key);
  return null;
};

/**
 * Set cached data
 */
const setCachedData = (key, data) => {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
};

/**
 * Build Moodle API request URL
 */
const buildApiUrl = (wsfunction, params = {}, token = null) => {
  const wstoken = token || config.defaultToken;
  const baseParams = {
    wstoken,
    wsfunction,
    moodlewsrestformat: 'json'
  };

  const allParams = { ...baseParams, ...params };
  const queryString = new URLSearchParams(allParams).toString();

  return `${config.baseUrl}?${queryString}`;
};

/**
 * Make API call to Moodle Web Service using fetcher
 */
const callApi = async (wsfunction, params = {}, token = null, retries = 2) => {
  const cacheKey = `${wsfunction}_${JSON.stringify(params)}_${token || 'default'}`;
  
  // Check cache first for GET-like operations
  if (['GET_TENANT_HEADER_INFO', 'GET_FOOTER_INFO', 'GET_PROGRAMS'].includes(wsfunction)) {
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      // Prepare form data for Moodle API
      const formData = new URLSearchParams();
      const wstoken = token || config.defaultToken;
      
      formData.append('wstoken', wstoken);
      formData.append('wsfunction', wsfunction);
      formData.append('moodlewsrestformat', 'json');
      
      // Add other parameters
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key]);
      });

      // Use postRequest from fetcher
      const response = await postRequest(config.baseUrl, formData);
      
      // Handle Moodle-specific response format
      if (response.success) {
        const data = response.data;
        
        // Check for Moodle API errors
        if (data.exception) {
          return {
            success: false,
            data: null,
            message: `Moodle API Error: ${data.message || data.exception}`,
            status: 400
          };
        }

        const result = {
          success: true,
          data: data,
          message: 'Success',
          status: 200
        };

        // Cache successful responses
        if (['GET_TENANT_HEADER_INFO', 'GET_FOOTER_INFO', 'GET_PROGRAMS'].includes(wsfunction)) {
          setCachedData(cacheKey, result);
        }

        return result;
      } else {
        return response; // Return the error response from fetcher
      }
    } catch (error) {
      if (attempt === retries) {
        return {
          success: false,
          data: null,
          message: error.message || 'Network error occurred',
          status: 500
        };
      }
      
      // Wait before retry (exponential backoff)
      await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

/**
 * Get Tenant Header Information
 */
const getTenantHeaderInfo = async (tenantId = null, lang = 'en', token = null) => {
  const params = {
    tenantid: tenantId || config.defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_TENANT_HEADER_INFO, params, token);
};

/**
 * Get Tenant Footer Information
 */
const getTenantFooterInfo = async (tenantId = null, lang = 'en', token = null) => {
  const params = {
    tenantid: tenantId || config.defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_FOOTER_INFO, params, token);
};

/**
 * Get Tenant Programs
 */
const getTenantPrograms = async (tenantId = null, lang = 'en', token = null) => {
  const params = {
    tenantid: tenantId || config.defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_PROGRAMS, params, token);
};

/**
 * Test API Connection
 */
const testConnection = async (token = null) => {
  try {
    const result = await getTenantHeaderInfo(null, 'en', token);
    return {
      success: result.success,
      connected: result.success,
      message: result.success ? 'Connection successful' : result.message,
      data: result.success ? result.data : null
    };
  } catch (error) {
    return {
      success: false,
      connected: false,
      message: error.message || 'Connection failed',
      data: null
    };
  }
};

/**
 * Get all tenant data at once
 */
const getAllTenantData = async (tenantId = null, lang = 'en', token = null) => {
  try {
    const [headerResult, footerResult, programsResult] = await Promise.allSettled([
      getTenantHeaderInfo(tenantId, lang, token),
      getTenantFooterInfo(tenantId, lang, token),
      getTenantPrograms(tenantId, lang, token)
    ]);

    return {
      success: true,
      data: {
        header: headerResult.status === 'fulfilled' ? headerResult.value : null,
        footer: footerResult.status === 'fulfilled' ? footerResult.value : null,
        programs: programsResult.status === 'fulfilled' ? programsResult.value : null
      },
      errors: {
        header: headerResult.status === 'rejected' ? headerResult.reason : null,
        footer: footerResult.status === 'rejected' ? footerResult.reason : null,
        programs: programsResult.status === 'rejected' ? programsResult.reason : null
      }
    };
  } catch (error) {
    return {
      success: false,
      data: null,
      message: error.message || 'Failed to fetch tenant data'
    };
  }
};

// Create service object
const multiTenantApi = {
  getTenantHeaderInfo,
  getTenantFooterInfo,
  getTenantPrograms,
  getAllTenantData,
  testConnection,
  callApi,
  buildApiUrl,
  clearCache
};

// Add global debug helper in development
if (process.env.NODE_ENV === 'development') {
  window.multiTenantApi = multiTenantApi;
}

export default multiTenantApi;

// Named exports for specific functions
export {
  getTenantHeaderInfo,
  getTenantFooterInfo,
  getTenantPrograms,
  getAllTenantData,
  testConnection,
  callApi,
  buildApiUrl,
  clearCache
};
