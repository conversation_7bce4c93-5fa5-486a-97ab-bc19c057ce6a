import { MOODLE_CONFIG, API_BASE_URL } from '../../config/config';
import { postRequest } from './fetcher';

/**
 * Multi-Tenant Moodle API Service
 * Handles all Moodle Web Service calls for multi-tenant functionality
 * Now uses the centralized fetcher for consistency
 */

// Private state for caching
let cache = new Map();
const cacheTimeout = 5 * 60 * 1000; // 5 minutes

// Configuration
const config = {
  baseUrl: `${API_BASE_URL}${MOODLE_CONFIG.BASE_URL}`,
  defaultToken: MOODLE_CONFIG.DEFAULT_WS_TOKEN,
  defaultTenant: MOODLE_CONFIG.DEFAULT_TENANT
};

/**
 * Clear cache
 */
const clearCache = () => {
  cache.clear();
};

/**
 * Get cached data
 * @param {string} key - Cache key
 * @returns {Object|null} Cached data or null
 */
const getCachedData = (key) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < cacheTimeout) {
    return cached.data;
  }
  cache.delete(key);
  return null;
};

/**
 * Set cached data
 * @param {string} key - Cache key
 * @param {Object} data - Data to cache
 */
const setCachedData = (key, data) => {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
};

/**
 * Build Moodle API request URL
 * @param {string} wsfunction - Web service function name
 * @param {Object} params - Additional parameters
 * @param {string} token - Web service token (optional)
 * @returns {string} Complete API URL
 */
const buildApiUrl = (wsfunction, params = {}, token = null) => {
  const wstoken = token || config.defaultToken;
  const baseParams = {
    wstoken,
    wsfunction,
    moodlewsrestformat: 'json'
  };

  const allParams = { ...baseParams, ...params };
  const queryString = new URLSearchParams(allParams).toString();

  return `${config.baseUrl}?${queryString}`;
};

/**
 * Make API call to Moodle Web Service using fetcher
 * @param {string} wsfunction - Web service function name
 * @param {Object} params - API parameters
 * @param {string} token - Web service token (optional)
 * @param {number} retries - Number of retry attempts
 * @returns {Promise<Object>} API response
 */
const callApi = async (wsfunction, params = {}, token = null, retries = 2) => {
  const cacheKey = `${wsfunction}_${JSON.stringify(params)}_${token || 'default'}`;

  // Check cache first for GET-like operations
  if (['GET_TENANT_HEADER_INFO', 'GET_FOOTER_INFO', 'GET_PROGRAMS'].includes(wsfunction)) {
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const url = buildApiUrl(wsfunction, params, token);

      // Use postRequest from fetcher instead of direct fetch
      // Note: We need to handle the URL differently since Moodle expects form data
      const formData = new URLSearchParams();
      const wstoken = token || config.defaultToken;

      formData.append('wstoken', wstoken);
      formData.append('wsfunction', wsfunction);
      formData.append('moodlewsrestformat', 'json');

      // Add other parameters
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key]);
      });

      // Make the request using our fetcher
      const response = await postRequest(config.baseUrl, formData);

      // Handle Moodle-specific response format
      if (response.success) {
        const data = response.data;

        // Check for Moodle API errors
        if (data.exception) {
          return {
            success: false,
            data: null,
            message: `Moodle API Error: ${data.message || data.exception}`,
            status: 400
          };
        }

        const result = {
          success: true,
          data: data,
          message: 'Success',
          status: 200
        };

        // Cache successful responses
        if (['GET_TENANT_HEADER_INFO', 'GET_FOOTER_INFO', 'GET_PROGRAMS'].includes(wsfunction)) {
          setCachedData(cacheKey, result);
        }

        return result;
      } else {
        return response; // Return the error response from fetcher
      }
    } catch (error) {
      if (attempt === retries) {
        return {
          success: false,
          data: null,
          message: error.message || 'Network error occurred',
          status: 500
        };
      }

      // Wait before retry (exponential backoff)
      await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

/**
 * Get Tenant Header Information
 * @param {number} tenantId - Tenant ID
 * @param {string} lang - Language code (en, ar, etc.)
 * @param {string} token - Web service token (optional)
 * @returns {Promise<Object>} Header information with consistent format
 */
const getTenantHeaderInfo = async (tenantId = null, lang = 'en', token = null) => {
  const params = {
    tenantid: tenantId || config.defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_TENANT_HEADER_INFO, params, token);
};

/**
 * Get Tenant Footer Information
 * @param {number} tenantId - Tenant ID
 * @param {string} lang - Language code
 * @param {string} token - Web service token (optional)
 * @returns {Promise<Object>} Footer information with consistent format
 */
const getTenantFooterInfo = async (tenantId = null, lang = 'en', token = null) => {
  const params = {
    tenantid: tenantId || config.defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_FOOTER_INFO, params, token);
};

/**
 * Get Tenant Programs
 * @param {number} tenantId - Tenant ID
 * @param {string} lang - Language code
 * @param {string} token - Web service token (optional)
 * @returns {Promise<Object>} Programs list with consistent format
 */
const getTenantPrograms = async (tenantId = null, lang = 'en', token = null) => {
  const params = {
    tenantid: tenantId || config.defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_PROGRAMS, params, token);
};

/**
 * Test API Connection
 * @param {string} token - Web service token (optional)
 * @returns {Promise<Object>} Connection status with details
 */
const testConnection = async (token = null) => {
  try {
    const result = await getTenantHeaderInfo(null, 'en', token);
    return {
      success: result.success,
      connected: result.success,
      message: result.success ? 'Connection successful' : result.message,
      data: result.success ? result.data : null
    };
  } catch (error) {
    return {
      success: false,
      connected: false,
      message: error.message || 'Connection failed',
      data: null
    };
  }
};

/**
 * Get all tenant data at once
 * @param {number} tenantId - Tenant ID
 * @param {string} lang - Language code
 * @param {string} token - Web service token (optional)
 * @returns {Promise<Object>} All tenant data
 */
const getAllTenantData = async (tenantId = null, lang = 'en', token = null) => {
  try {
    const [headerResult, footerResult, programsResult] = await Promise.allSettled([
      getTenantHeaderInfo(tenantId, lang, token),
      getTenantFooterInfo(tenantId, lang, token),
      getTenantPrograms(tenantId, lang, token)
    ]);

    return {
      success: true,
      data: {
        header: headerResult.status === 'fulfilled' ? headerResult.value : null,
        footer: footerResult.status === 'fulfilled' ? footerResult.value : null,
        programs: programsResult.status === 'fulfilled' ? programsResult.value : null
      },
      errors: {
        header: headerResult.status === 'rejected' ? headerResult.reason : null,
        footer: footerResult.status === 'rejected' ? footerResult.reason : null,
        programs: programsResult.status === 'rejected' ? programsResult.reason : null
      }
    };
  } catch (error) {
    return {
      success: false,
      data: null,
      message: error.message || 'Failed to fetch tenant data'
    };
  }
};

// Create service object
const multiTenantApi = {
  getTenantHeaderInfo,
  getTenantFooterInfo,
  getTenantPrograms,
  getAllTenantData,
  testConnection,
  callApi,
  buildApiUrl,
  clearCache
};

// Add global debug helper in development
if (process.env.NODE_ENV === 'development') {
  window.multiTenantApi = multiTenantApi;
}

export default multiTenantApi;

// Named exports for specific functions
export const {
  getTenantHeaderInfo,
  getTenantFooterInfo,
  getTenantPrograms,
  getAllTenantData,
  testConnection,
  callApi,
  buildApiUrl,
  clearCache
} = multiTenantApi;
