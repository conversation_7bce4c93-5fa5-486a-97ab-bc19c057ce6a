import { MOODLE_CONFIG, API_BASE_URL } from '../../config/config';

/**
 * Multi-Tenant Moodle API Service
 * Handles all Moodle Web Service calls for multi-tenant functionality
 */
class MultiTenantApi {
  constructor() {
    this.baseUrl = `${API_BASE_URL}${MOODLE_CONFIG.BASE_URL}`;
    this.defaultToken = MOODLE_CONFIG.DEFAULT_WS_TOKEN;
    this.defaultTenant = MOODLE_CONFIG.DEFAULT_TENANT;
  }

  /**
   * Build Moodle API request URL
   * @param {string} wsfunction - Web service function name
   * @param {Object} params - Additional parameters
   * @param {string} token - Web service token (optional)
   * @returns {string} Complete API URL
   */
  buildApiUrl(wsfunction, params = {}, token = null) {
    const wstoken = token || this.defaultToken;
    const baseParams = {
      wstoken,
      wsfunction,
      moodlewsrestformat: 'json'
    };

    const allParams = { ...baseParams, ...params };
    const queryString = new URLSearchParams(allParams).toString();

    return `${this.baseUrl}?${queryString}`;
  }

  /**
   * Make API call to Moodle Web Service
   * @param {string} wsfunction - Web service function name
   * @param {Object} params - API parameters
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} API response
   */
  async callApi(wsfunction, params = {}, token = null) {
    try {
      const url = this.buildApiUrl(wsfunction, params, token);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.exception) {
        throw new Error(`Moodle API Error: ${data.message || data.exception}`);
      }

      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get Tenant Header Information
   * @param {number} tenantId - Tenant ID
   * @param {string} lang - Language code (en, ar, etc.)
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} Header information
   */
  async getTenantHeaderInfo(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_TENANT_HEADER_INFO, params, token);
  }

  /**
   * Get Tenant Footer Information
   * @param {number} tenantId - Tenant ID
   * @param {string} lang - Language code
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} Footer information
   */
  async getTenantFooterInfo(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_FOOTER_INFO, params, token);
  }

  /**
   * Get Tenant Programs
   * @param {number} tenantId - Tenant ID
   * @param {string} lang - Language code
   * @param {string} token - Web service token (optional)
   * @returns {Promise<Object>} Programs list
   */
  async getTenantPrograms(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_PROGRAMS, params, token);
  }

  /**
   * Test API Connection
   * @param {string} token - Web service token (optional)
   * @returns {Promise<boolean>} Connection status
   */
  async testConnection(token = null) {
    try {
      await this.getTenantHeaderInfo(null, 'en', token);
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Create singleton instance
const multiTenantApi = new MultiTenantApi();

export default multiTenantApi;

// Named exports for specific functions
export const { getTenantHeaderInfo, getTenantFooterInfo, getTenantPrograms, testConnection, callApi, buildApiUrl } = multiTenantApi;
