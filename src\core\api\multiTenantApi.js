import { MOODLE_CONFIG, API_BASE_URL } from '../../config/config';

class MultiTenantApi {
  constructor() {
    this.baseUrl = `${API_BASE_URL}${MOODLE_CONFIG.BASE_URL}`;
    this.defaultToken = MOODLE_CONFIG.DEFAULT_WS_TOKEN;
    this.defaultTenant = MOODLE_CONFIG.DEFAULT_TENANT;
  }

  buildApiUrl(wsfunction, params = {}, token = null) {
    const wstoken = token || this.defaultToken;
    const baseParams = {
      wstoken,
      wsfunction,
      moodlewsrestformat: 'json'
    };

    const allParams = { ...baseParams, ...params };
    const queryString = new URLSearchParams(allParams).toString();

    return `${this.baseUrl}?${queryString}`;
  }

  async callApi(wsfunction, params = {}, token = null) {
    try {
      const url = this.buildApiUrl(wsfunction, params, token);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.exception) {
        throw new Error(`Moodle API Error: ${data.message || data.exception}`);
      }

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getTenantHeaderInfo(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_TENANT_HEADER_INFO, params, token);
  }

  async getTenantFooterInfo(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_FOOTER_INFO, params, token);
  }

  async getTenantPrograms(tenantId = null, lang = 'en', token = null) {
    const params = {
      tenantid: tenantId || this.defaultTenant.id,
      lang
    };

    return await this.callApi(MOODLE_CONFIG.FUNCTIONS.GET_PROGRAMS, params, token);
  }

  async testConnection(token = null) {
    try {
      await this.getTenantHeaderInfo(null, 'en', token);
      return true;
    } catch (error) {
      return false;
    }
  }
}

const multiTenantApi = new MultiTenantApi();

export default multiTenantApi;

// Named exports for specific functions
export const { getTenantHeaderInfo, getTenantFooterInfo, getTenantPrograms, testConnection, callApi, buildApiUrl } = multiTenantApi;
