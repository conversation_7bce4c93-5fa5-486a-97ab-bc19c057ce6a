// src/examples/ServiceUsageExample.jsx
import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, CircularProgress, Alert, Card, CardContent } from '@mui/material';

// Import services
import authService from '../services/authService';
import coursesService from '../services/coursesService';
import programsService from '../services/programsService';
import tenantService from '../services/tenantServiceNew';

// Import hooks
import { useCourses } from '../hooks/useCourses';
import { useTenantData } from '../hooks/useTenantData';

/**
 * Example component showing how to use the new service architecture
 */
const ServiceUsageExample = () => {
  // State for different examples
  const [authData, setAuthData] = useState(null);
  const [tenantData, setTenantData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Using custom hooks
  const { courses, loading: coursesLoading, fetchCourses } = useCourses();
  const { 
    headerInfo, 
    tenantColors, 
    tenantName, 
    loading: tenantLoading 
  } = useTenantData(83, 'en');

  // Example 1: Direct service usage
  const handleAuthExample = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Check if user is authenticated
      const isAuth = authService.isAuthenticated();
      const currentUser = authService.getCurrentUser();
      
      setAuthData({
        isAuthenticated: isAuth,
        user: currentUser,
        token: authService.getToken()
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Example 2: Tenant service usage
  const handleTenantExample = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Set tenant context
      tenantService.setContext(83, 'en');
      
      // Get all tenant data
      const result = await tenantService.getAllData();
      
      if (result.success) {
        setTenantData(result.data);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Example 3: Courses service usage
  const handleCoursesExample = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Get all courses
      const coursesResult = await coursesService.getAllCourses();
      
      if (coursesResult.success) {
        console.log('Courses:', coursesResult.data);
        
        // Get enrolled courses
        const enrolledResult = await coursesService.getEnrolledCourses();
        console.log('Enrolled courses:', enrolledResult.data);
        
        // Search courses
        const searchResult = await coursesService.searchCourses('javascript');
        console.log('Search results:', searchResult.data);
      } else {
        setError(coursesResult.message);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Example 4: Programs service usage
  const handleProgramsExample = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Get all programs
      const programsResult = await programsService.getAllPrograms(83, 'en');
      
      if (programsResult.success) {
        console.log('Programs:', programsResult.data);
        
        // Get enrolled programs
        const enrolledResult = await programsService.getEnrolledPrograms();
        console.log('Enrolled programs:', enrolledResult.data);
      } else {
        setError(programsResult.message);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Example 5: File upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setLoading(true);
    setError(null);

    try {
      // Using the uploadFile function from fetcher
      const { uploadFile } = await import('../core/api/fetcher');
      
      const result = await uploadFile('/api/upload', file, {
        type: 'profile-image',
        userId: authService.getCurrentUser()?.id
      });

      if (result.success) {
        console.log('File uploaded:', result.data);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Service Architecture Examples
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Example 1: Auth Service */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            1. Auth Service Example
          </Typography>
          <Button 
            variant="contained" 
            onClick={handleAuthExample}
            disabled={loading}
            sx={{ mr: 2 }}
          >
            Check Auth Status
          </Button>
          {authData && (
            <Box sx={{ mt: 2 }}>
              <Typography>Authenticated: {authData.isAuthenticated ? 'Yes' : 'No'}</Typography>
              <Typography>User: {authData.user?.name || 'None'}</Typography>
              <Typography>Token: {authData.token ? 'Present' : 'None'}</Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Example 2: Tenant Service */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            2. Tenant Service Example
          </Typography>
          <Button 
            variant="contained" 
            onClick={handleTenantExample}
            disabled={loading}
            sx={{ mr: 2 }}
          >
            Get Tenant Data
          </Button>
          {tenantData && (
            <Box sx={{ mt: 2 }}>
              <Typography>Header: {tenantData.header ? 'Loaded' : 'Not loaded'}</Typography>
              <Typography>Footer: {tenantData.footer ? 'Loaded' : 'Not loaded'}</Typography>
              <Typography>Programs: {tenantData.programs ? 'Loaded' : 'Not loaded'}</Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Example 3: Using Hooks */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            3. Custom Hooks Example
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Typography>Tenant Name: {tenantName}</Typography>
            <Typography>Primary Color: {tenantColors.primary}</Typography>
            <Typography>Loading: {tenantLoading ? 'Yes' : 'No'}</Typography>
          </Box>
          <Button 
            variant="contained" 
            onClick={fetchCourses}
            disabled={coursesLoading}
            sx={{ mr: 2 }}
          >
            Fetch Courses
          </Button>
          <Typography>Courses Count: {courses.length}</Typography>
        </CardContent>
      </Card>

      {/* Example 4: Courses Service */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            4. Courses Service Example
          </Typography>
          <Button 
            variant="contained" 
            onClick={handleCoursesExample}
            disabled={loading}
            sx={{ mr: 2 }}
          >
            Test Courses API
          </Button>
          <Typography variant="body2">
            Check console for results
          </Typography>
        </CardContent>
      </Card>

      {/* Example 5: Programs Service */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            5. Programs Service Example
          </Typography>
          <Button 
            variant="contained" 
            onClick={handleProgramsExample}
            disabled={loading}
            sx={{ mr: 2 }}
          >
            Test Programs API
          </Button>
          <Typography variant="body2">
            Check console for results
          </Typography>
        </CardContent>
      </Card>

      {/* Example 6: File Upload */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            6. File Upload Example
          </Typography>
          <input
            type="file"
            onChange={handleFileUpload}
            accept="image/*"
            disabled={loading}
          />
          <Typography variant="body2" sx={{ mt: 1 }}>
            Upload an image file
          </Typography>
        </CardContent>
      </Card>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default ServiceUsageExample;
