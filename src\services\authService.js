// src/services/authService.js
import { getRequest, postRequest } from '../core/api/fetcher';
import { API_ENDPOINTS } from '../config/config';

/**
 * Login user
 * @param {Object} credentials - User credentials
 * @returns {Promise<Object>} Login response
 */
const login = async (credentials) => {
  try {
    const response = await postRequest(API_ENDPOINTS.LOGIN, credentials);
    if (response.success) {
      localStorage.setItem('serviceToken', response.data.token);
      localStorage.setItem('userData', JSON.stringify(response.data.user));
    }
    return response;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

/**
 * Logout user
 * @returns {Promise<Object>} Logout response
 */
const logout = async () => {
  try {
    await postRequest(API_ENDPOINTS.LOGOUT);
    localStorage.removeItem('serviceToken');
    localStorage.removeItem('userData');
    return { success: true };
  } catch (error) {
    // Even if API call fails, clear local storage
    localStorage.removeItem('serviceToken');
    localStorage.removeItem('userData');
    return { success: true };
  }
};

/**
 * Get current user from localStorage
 * @returns {Object|null} User data or null
 */
const getCurrentUser = () => {
  const userData = localStorage.getItem('userData');
  return userData ? JSON.parse(userData) : null;
};

/**
 * Check if user is authenticated
 * @returns {boolean} Authentication status
 */
const isAuthenticated = () => {
  return !!localStorage.getItem('serviceToken');
};

/**
 * Get authentication token
 * @returns {string|null} Token or null
 */
const getToken = () => {
  return localStorage.getItem('serviceToken');
};

/**
 * Update user profile
 * @param {Object} profileData - Profile data to update
 * @returns {Promise<Object>} Update response
 */
const updateProfile = async (profileData) => {
  try {
    const response = await postRequest('/api/user/update-profile', profileData);
    if (response.success) {
      localStorage.setItem('userData', JSON.stringify(response.data));
    }
    return response;
  } catch (error) {
    console.error('Update profile error:', error);
    throw error;
  }
};

/**
 * Change user password
 * @param {Object} passwordData - Old and new password
 * @returns {Promise<Object>} Change password response
 */
const changePassword = async (passwordData) => {
  try {
    const response = await postRequest('/api/user/change-password', passwordData);
    return response;
  } catch (error) {
    console.error('Change password error:', error);
    throw error;
  }
};

/**
 * Request password reset
 * @param {string} email - User email
 * @returns {Promise<Object>} Reset request response
 */
const requestPasswordReset = async (email) => {
  try {
    const response = await postRequest('/api/auth/forgot-password', { email });
    return response;
  } catch (error) {
    console.error('Password reset request error:', error);
    throw error;
  }
};

/**
 * Reset password with token
 * @param {Object} resetData - Reset token and new password
 * @returns {Promise<Object>} Reset response
 */
const resetPassword = async (resetData) => {
  try {
    const response = await postRequest('/api/auth/reset-password', resetData);
    return response;
  } catch (error) {
    console.error('Password reset error:', error);
    throw error;
  }
};

// Export all functions as an object
const authService = {
  login,
  logout,
  getCurrentUser,
  isAuthenticated,
  getToken,
  updateProfile,
  changePassword,
  requestPasswordReset,
  resetPassword
};

export default authService;
