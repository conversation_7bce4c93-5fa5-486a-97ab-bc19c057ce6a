import PropTypes from 'prop-types';
import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { Container } from '@mui/material';

// project-imports
import Loader from 'components/Loader';
import { SimpleLayoutType } from 'config';
import { Box } from '@mui/material';
import useMultiTenant from '../../hooks/useMultiTenant';
import useConfig from '../../hooks/useConfig';

const Header = lazy(() => import('./Header'));
const FooterBlock = lazy(() => import('./FooterBlock'));

// ==============================|| LAYOUT - SIMPLE / LANDING ||============================== //

export default function SimpleLayout({ layout = SimpleLayoutType.SIMPLE }) {
  const { i18n, onChangeLocalization } = useConfig();

  const { headerInfo: backendHeaderInfo, isConnected } = useMultiTenant(83, i18n);
  return (
    <>
      {}
      <Suspense fallback={<Loader />}>
        {isConnected && (
          <Box
            sx={{
              pt: 2,
              minHeight: '100vh',
              background: '#ede9ff'
            }}
          >
            <Header />
            <Box sx={{ mx: 'auto', width: '96%' }}>
              <Outlet />
            </Box>
            <FooterBlock />
          </Box>
        )}
      </Suspense>
    </>
  );
}

SimpleLayout.propTypes = { layout: PropTypes.any };
