// src/hooks/useTenantData.js
import { useState, useEffect, useCallback } from 'react';
import tenantService from '../services/tenantService';

/**
 * Enhanced Tenant Hook
 * Provides a clean interface for tenant data management
 */
export const useTenantData = (tenantId = null, language = 'en') => {
  const [headerInfo, setHeaderInfo] = useState(null);
  const [footerInfo, setFooterInfo] = useState(null);
  const [programs, setPrograms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  // Set tenant context when hook initializes
  useEffect(() => {
    if (tenantId && language) {
      tenantService.setContext(tenantId, language);
    }
  }, [tenantId, language]);

  // Load header information
  const loadHeaderInfo = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await tenantService.getHeaderInfo(tenantId, language);
      if (result.success) {
        setHeaderInfo(result.data);
        setIsConnected(true);
      } else {
        setError(result.message);
        setIsConnected(false);
      }
      return result;
    } catch (err) {
      setError(err.message);
      setIsConnected(false);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [tenantId, language]);

  // Load footer information
  const loadFooterInfo = useCallback(async () => {
    try {
      const result = await tenantService.getFooterInfo(tenantId, language);
      if (result.success) {
        setFooterInfo(result.data);
      }
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, [tenantId, language]);

  // Load programs
  const loadPrograms = useCallback(async () => {
    try {
      const result = await tenantService.getPrograms(tenantId, language);
      if (result.success) {
        setPrograms(result.data);
      }
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, [tenantId, language]);

  // Load all tenant data at once
  const loadAllData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await tenantService.getAllData(tenantId, language);
      if (result.success) {
        if (result.data.header) setHeaderInfo(result.data.header);
        if (result.data.footer) setFooterInfo(result.data.footer);
        if (result.data.programs) setPrograms(result.data.programs);
        setIsConnected(true);
      } else {
        setError(result.message);
        setIsConnected(false);
      }
      return result;
    } catch (err) {
      setError(err.message);
      setIsConnected(false);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [tenantId, language]);

  // Test connection
  const testConnection = useCallback(async () => {
    try {
      const result = await tenantService.testConnection();
      setIsConnected(result.connected);
      return result;
    } catch (err) {
      setIsConnected(false);
      throw err;
    }
  }, []);

  // Refresh all data
  const refresh = useCallback(async () => {
    tenantService.clearCache();
    return await loadAllData();
  }, [loadAllData]);

  // Auto-load data when hook mounts or dependencies change
  useEffect(() => {
    if (tenantId) {
      loadAllData();
    }
  }, [tenantId, language, loadAllData]);

  return {
    // Data
    headerInfo,
    footerInfo,
    programs,
    
    // Status
    loading,
    error,
    isConnected,
    
    // Actions
    loadHeaderInfo,
    loadFooterInfo,
    loadPrograms,
    loadAllData,
    testConnection,
    refresh,
    
    // Computed values (for backward compatibility)
    tenantName: headerInfo?.name || 'Default Tenant',
    tenantLogo: headerInfo?.logo,
    tenantColors: headerInfo?.colors || {
      primary: '#8c76dd',
      secondary: '#FFE284',
      tertiary: '#FF8A19'
    },
    availableServices: headerInfo?.services || [],
    hasAboutUs: headerInfo?.features?.hasAboutUs || false,
    hasContactUs: headerInfo?.features?.hasContactUs || false,
    hasFaq: headerInfo?.features?.hasFaq || false,
    hasServices: headerInfo?.features?.hasServices || false,
    
    // Utilities
    clearError: () => setError(null),
    clearCache: tenantService.clearCache
  };
};

// Simplified hook for just header info (most common use case)
export const useTenantHeader = (tenantId = null, language = 'en') => {
  const [headerInfo, setHeaderInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadHeader = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await tenantService.getHeaderInfo(tenantId, language);
      if (result.success) {
        setHeaderInfo(result.data);
      } else {
        setError(result.message);
      }
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [tenantId, language]);

  useEffect(() => {
    if (tenantId) {
      loadHeader();
    }
  }, [tenantId, language, loadHeader]);

  return {
    headerInfo,
    loading,
    error,
    loadHeader,
    tenantName: headerInfo?.name || 'Default Tenant',
    tenantLogo: headerInfo?.logo,
    tenantColors: headerInfo?.colors || {
      primary: '#8c76dd',
      secondary: '#FFE284',
      tertiary: '#FF8A19'
    }
  };
};

export default useTenantData;
