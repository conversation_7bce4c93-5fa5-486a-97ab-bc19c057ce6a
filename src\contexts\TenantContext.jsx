// src/contexts/TenantContext.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { tenantApi } from '../core/api/tenantApi';

const TenantContext = createContext();

// Provider Component
export const TenantProvider = ({ children }) => {
  const [currentTenant, setCurrentTenant] = useState(null);
  const [tenantHeaderInfo, setTenantHeaderInfo] = useState(null);
  const [tenantFooterInfo, setTenantFooterInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let isMounted = true;
    const loadTenant = async () => {
      try {
        //  const tenantData = await fetchTenantFromAPI()

        const savedTenant = localStorage.getItem('currentTenant');
        if (savedTenant && isMounted) {
          const tenantData = JSON.parse(savedTenant);
          setCurrentTenant(tenantData);
        }
      } catch (err) {
        console.error('Error loading tenant:', err);
        if (isMounted) {
          setError(err.message);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadTenant();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  const setTenant = (tenant) => {
    try {
      if (!tenant) {
        clearTenant();
        return;
      }

      setCurrentTenant(tenant);
      localStorage.setItem('currentTenant', JSON.stringify(tenant));
      setError(null);
    } catch (err) {
      console.error('Error setting tenant:', err);
      setError(err.message);
    }
  };

  const clearTenant = () => {
    setCurrentTenant(null);
    localStorage.removeItem('currentTenant');
    setError(null);
  };

  const switchTenant = (tenantData) => {
    try {
      if (tenantData) {
        setTenant(tenantData);
        // Clear cached Moodle data when switching tenants
        setTenantHeaderInfo(null);
        setTenantFooterInfo(null);
      } else {
        throw new Error('Tenant data is required');
      }
    } catch (err) {
      console.error('Error switching tenant:', err);
      setError(err.message);
    }
  };

  /**
   * Load tenant header info from Moodle API
   */
  const loadTenantHeaderInfo = async (lang = 'en') => {
    if (!currentTenant?.id) return null;

    try {
      const headerInfo = await tenantApi.getTenantHeaderInfo(currentTenant.id, lang);
      setTenantHeaderInfo(headerInfo);
      return headerInfo;
    } catch (err) {
      console.error('Failed to load tenant header info:', err);
      setError(err.message);
      return null;
    }
  };

  /**
   * Load tenant footer info from Moodle API
   */
  const loadTenantFooterInfo = async (lang = 'en') => {
    if (!currentTenant?.id) return null;

    try {
      const footerInfo = await tenantApi.getTenantFooterInfo(currentTenant.id, lang);
      setTenantFooterInfo(footerInfo);
      return footerInfo;
    } catch (err) {
      console.error('Failed to load tenant footer info:', err);
      setError(err.message);
      return null;
    }
  };

  return (
    <TenantContext.Provider
      value={{
        currentTenant,
        tenantHeaderInfo,
        tenantFooterInfo,
        setTenant,
        clearTenant,
        switchTenant,
        loadTenantHeaderInfo,
        loadTenantFooterInfo,
        isLoading,
        error
      }}
    >
      {children}
    </TenantContext.Provider>
  );
};

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

export default TenantContext;
