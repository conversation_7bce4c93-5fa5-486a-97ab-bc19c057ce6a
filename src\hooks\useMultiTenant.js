import { useState, useEffect, useCallback } from 'react';
import multiTenantApi from '../core/api/multiTenantApi';
import { MOODLE_CONFIG } from '../config/config';

/**
 * Multi-Tenant Hook
 * Manages tenant-specific data and API calls
 */
export const useMultiTenant = (tenantId = null, language = 'en') => {
  // State management
  const [headerInfo, setHeaderInfo] = useState(null);
  const [footerInfo, setFooterInfo] = useState(null);
  const [programs, setPrograms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  // Current tenant configuration
  const currentTenant = {
    id: tenantId || MOODLE_CONFIG.DEFAULT_TENANT.id,
    language: language || MOODLE_CONFIG.DEFAULT_TENANT.language
  };

  /**
   * Load Header Information
   */
  const loadHeaderInfo = useCallback(
    async (token = null) => {
      try {
        setLoading(true);
        setError(null);

        const data = await multiTenantApi.getTenantHeaderInfo(currentTenant.id, currentTenant.language, token);

        setHeaderInfo(data);
        setIsConnected(true);
        return data;
      } catch (err) {
        setError(err.message);
        setIsConnected(false);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [currentTenant.id, currentTenant.language]
  );

  /**
   * Load Footer Information
   */
  const loadFooterInfo = useCallback(
    async (token = null) => {
      try {
        setLoading(true);
        setError(null);

        const data = await multiTenantApi.getTenantFooterInfo(currentTenant.id, currentTenant.language, token);

        setFooterInfo(data);
        return data;
      } catch (err) {
        setError(err.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [currentTenant.id, currentTenant.language]
  );

  /**
   * Load Programs
   */
  const loadPrograms = useCallback(
    async (token = null) => {
      try {
        setLoading(true);
        setError(null);

        const data = await multiTenantApi.getTenantPrograms(currentTenant.id, currentTenant.language, token);

        setPrograms(data || []);
        return data;
      } catch (err) {
        setError(err.message);
        return [];
      } finally {
        setLoading(false);
      }
    },
    [currentTenant.id, currentTenant.language]
  );

  /**
   * Test API Connection
   */
  const testConnection = useCallback(async (token = null) => {
    try {
      const isConnected = await multiTenantApi.testConnection(token);
      setIsConnected(isConnected);
      return isConnected;
    } catch (err) {
      setIsConnected(false);
      return false;
    }
  }, []);

  /**
   * Load All Tenant Data
   */
  const loadAllTenantData = useCallback(
    async (token = null) => {
      setLoading(true);
      setError(null);

      try {
        const [header, footer, programsList] = await Promise.allSettled([
          loadHeaderInfo(token),
          loadFooterInfo(token),
          loadPrograms(token)
        ]);

        return {
          header: header.status === 'fulfilled' ? header.value : null,
          footer: footer.status === 'fulfilled' ? footer.value : null,
          programs: programsList.status === 'fulfilled' ? programsList.value : []
        };
      } catch (err) {
        setError(err.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [loadHeaderInfo, loadFooterInfo, loadPrograms]
  );

  /**
   * Refresh tenant data
   */
  const refresh = useCallback(
    (token = null) => {
      return loadAllTenantData(token);
    },
    [loadAllTenantData]
  );

  // Auto-load data on mount or tenant change
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Auto-load header info on mount
      loadHeaderInfo();
    }
  }, [currentTenant.id, currentTenant.language, loadHeaderInfo]);

  // Return hook interface
  return {
    // Data
    headerInfo,
    footerInfo,
    programs,
    currentTenant,

    // Status
    loading,
    error,
    isConnected,

    // Actions
    loadHeaderInfo,
    loadFooterInfo,
    loadPrograms,
    loadAllTenantData,
    testConnection,
    refresh,

    // Computed values
    tenantName: headerInfo?.name || 'Default Tenant',
    tenantLogo: headerInfo?.logo,
    tenantColors: {
      primary: headerInfo?.primarycolor || '#8c76dd',
      secondary: headerInfo?.secondarycolor || '#FFE284',
      tertiary: headerInfo?.teritorycolor || '#FF8A19'
    },
    availableServices: headerInfo?.services?.length > 0 ? headerInfo?.services : [],
    hasAboutUs: headerInfo?.aboutus?.[0]?.status === '1',
    hasContactUs: !!headerInfo?.contactus,
    hasFaq: headerInfo?.faq?.[0]?.status === '1'
  };
};

export default useMultiTenant;
